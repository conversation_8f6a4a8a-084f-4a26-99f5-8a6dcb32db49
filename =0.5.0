Defaulting to user installation because normal site-packages is not writeable
Collecting tiktoken
  Downloading tiktoken-0.9.0-cp313-cp313-win_amd64.whl.metadata (6.8 kB)
Collecting regex>=2022.1.18 (from tiktoken)
  Downloading regex-2024.11.6-cp313-cp313-win_amd64.whl.metadata (41 kB)
Requirement already satisfied: requests>=2.26.0 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from tiktoken) (2.32.3)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from requests>=2.26.0->tiktoken) (3.4.2)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from requests>=2.26.0->tiktoken) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from requests>=2.26.0->tiktoken) (2.4.0)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from requests>=2.26.0->tiktoken) (2025.4.26)
Downloading tiktoken-0.9.0-cp313-cp313-win_amd64.whl (894 kB)
   ---------------------------------------- 894.7/894.7 kB 3.3 MB/s eta 0:00:00
Downloading regex-2024.11.6-cp313-cp313-win_amd64.whl (273 kB)
Installing collected packages: regex, tiktoken
Successfully installed regex-2024.11.6 tiktoken-0.9.0
