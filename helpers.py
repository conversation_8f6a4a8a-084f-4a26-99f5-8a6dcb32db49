"""Shared helper functions for AI memory system."""

from typing import List
from openai import Async<PERSON>penA<PERSON>
from config import config


async def get_embedding(text: str, client: AsyncOpenAI = None) -> List[float]:
    """
    Generate embedding for given text using OpenAI.
    
    Args:
        text: Text to generate embedding for
        client: Optional OpenAI client instance
        
    Returns:
        List of float values representing the embedding
        
    Raises:
        RuntimeError: If embedding generation fails
    """
    if client is None:
        client = AsyncOpenAI(api_key=config.openai_api_key)
    
    try:
        response = await client.embeddings.create(
            model=config.embedding_model,
            input=text.strip(),
            encoding_format="float"
        )
        return response.data[0].embedding
    except Exception as e:
        raise RuntimeError(f"Failed to generate embedding: {e}")
