Folder PATH listing for volume Windows-SSD
Volume serial number is F068-B697
C:\REPOS\SPARKY-SANDBOX
|   .env
|   .env.example
|   =0.5.0
|   =1.24.0
|   BATCH_LOADING.md
|   config.py
|   helpers.py
|   inject_memory.py
|   load_memory_batch.py
|   package-lock.json
|   package.json
|   README.md
|   requirements.txt
|   retrieve_similar.py
|   sample_chat.md
|   SECURITY.md
|   structure.txt
|   test_batch_loading.py
|   test_memory_system.py
|   
+---.memory-bank
|       task-log_15-01-25-14-30.log
|       task-log_15-01-25-15-45.log
|       
+---node_modules
|   |   .package-lock.json
|   |   
|   +---.bin
|   |       mkdirp
|   |       mkdirp.cmd
|   |       mkdirp.ps1
|   |       supabase
|   |       supabase.cmd
|   |       supabase.ps1
|   |       
|   +---@isaacs
|   |   \---fs-minipass
|   |       |   LICENSE
|   |       |   package.json
|   |       |   README.md
|   |       |   
|   |       \---dist
|   |           +---commonjs
|   |           |       index.d.ts
|   |           |       index.d.ts.map
|   |           |       index.js
|   |           |       index.js.map
|   |           |       package.json
|   |           |       
|   |           \---esm
|   |                   index.d.ts
|   |                   index.d.ts.map
|   |                   index.js
|   |                   index.js.map
|   |                   package.json
|   |                   
|   +---agent-base
|   |   |   LICENSE
|   |   |   package.json
|   |   |   README.md
|   |   |   
|   |   \---dist
|   |           helpers.d.ts
|   |           helpers.d.ts.map
|   |           helpers.js
|   |           helpers.js.map
|   |           index.d.ts
|   |           index.d.ts.map
|   |           index.js
|   |           index.js.map
|   |           
|   +---bin-links
|   |   |   LICENSE
|   |   |   package.json
|   |   |   README.md
|   |   |   
|   |   \---lib
|   |           bin-target.js
|   |           check-bin.js
|   |           check-bins.js
|   |           fix-bin.js
|   |           get-node-modules.js
|   |           get-paths.js
|   |           get-prefix.js
|   |           index.js
|   |           is-windows.js
|   |           link-bin.js
|   |           link-bins.js
|   |           link-gently.js
|   |           link-mans.js
|   |           man-target.js
|   |           shim-bin.js
|   |           
|   +---chownr
|   |   |   LICENSE.md
|   |   |   package.json
|   |   |   README.md
|   |   |   
|   |   \---dist
|   |       +---commonjs
|   |       |       index.d.ts
|   |       |       index.d.ts.map
|   |       |       index.js
|   |       |       index.js.map
|   |       |       package.json
|   |       |       
|   |       \---esm
|   |               index.d.ts
|   |               index.d.ts.map
|   |               index.js
|   |               index.js.map
|   |               package.json
|   |               
|   +---cmd-shim
|   |   |   LICENSE
|   |   |   package.json
|   |   |   README.md
|   |   |   
|   |   \---lib
|   |           index.js
|   |           to-batch-syntax.js
|   |           
|   +---data-uri-to-buffer
|   |   |   package.json
|   |   |   README.md
|   |   |   
|   |   +---dist
|   |   |       index.d.ts
|   |   |       index.js
|   |   |       index.js.map
|   |   |       
|   |   \---src
|   |           index.ts
|   |           
|   +---debug
|   |   |   LICENSE
|   |   |   package.json
|   |   |   README.md
|   |   |   
|   |   \---src
|   |           browser.js
|   |           common.js
|   |           index.js
|   |           node.js
|   |           
|   +---fetch-blob
|   |       file.d.ts
|   |       file.js
|   |       from.d.ts
|   |       from.js
|   |       index.d.ts
|   |       index.js
|   |       LICENSE
|   |       package.json
|   |       README.md
|   |       streams.cjs
|   |       
|   +---formdata-polyfill
|   |       esm.min.d.ts
|   |       esm.min.js
|   |       formdata-to-blob.js
|   |       FormData.js
|   |       formdata.min.js
|   |       LICENSE
|   |       package.json
|   |       README.md
|   |       
|   +---https-proxy-agent
|   |   |   LICENSE
|   |   |   package.json
|   |   |   README.md
|   |   |   
|   |   \---dist
|   |           index.d.ts
|   |           index.d.ts.map
|   |           index.js
|   |           index.js.map
|   |           parse-proxy-response.d.ts
|   |           parse-proxy-response.d.ts.map
|   |           parse-proxy-response.js
|   |           parse-proxy-response.js.map
|   |           
|   +---imurmurhash
|   |       imurmurhash.js
|   |       imurmurhash.min.js
|   |       package.json
|   |       README.md
|   |       
|   +---minipass
|   |   |   LICENSE
|   |   |   package.json
|   |   |   README.md
|   |   |   
|   |   \---dist
|   |       +---commonjs
|   |       |       index.d.ts
|   |       |       index.d.ts.map
|   |       |       index.js
|   |       |       index.js.map
|   |       |       package.json
|   |       |       
|   |       \---esm
|   |               index.d.ts
|   |               index.d.ts.map
|   |               index.js
|   |               index.js.map
|   |               package.json
|   |               
|   +---minizlib
|   |   |   LICENSE
|   |   |   package.json
|   |   |   README.md
|   |   |   
|   |   \---dist
|   |       +---commonjs
|   |       |       constants.d.ts
|   |       |       constants.d.ts.map
|   |       |       constants.js
|   |       |       constants.js.map
|   |       |       index.d.ts
|   |       |       index.d.ts.map
|   |       |       index.js
|   |       |       index.js.map
|   |       |       package.json
|   |       |       
|   |       \---esm
|   |               constants.d.ts
|   |               constants.d.ts.map
|   |               constants.js
|   |               constants.js.map
|   |               index.d.ts
|   |               index.d.ts.map
|   |               index.js
|   |               index.js.map
|   |               package.json
|   |               
|   +---mkdirp
|   |   |   LICENSE
|   |   |   package.json
|   |   |   readme.markdown
|   |   |   
|   |   \---dist
|   |       +---cjs
|   |       |   |   package.json
|   |       |   |   
|   |       |   \---src
|   |       |           bin.d.ts
|   |       |           bin.d.ts.map
|   |       |           bin.js
|   |       |           bin.js.map
|   |       |           find-made.d.ts
|   |       |           find-made.d.ts.map
|   |       |           find-made.js
|   |       |           find-made.js.map
|   |       |           index.d.ts
|   |       |           index.d.ts.map
|   |       |           index.js
|   |       |           index.js.map
|   |       |           mkdirp-manual.d.ts
|   |       |           mkdirp-manual.d.ts.map
|   |       |           mkdirp-manual.js
|   |       |           mkdirp-manual.js.map
|   |       |           mkdirp-native.d.ts
|   |       |           mkdirp-native.d.ts.map
|   |       |           mkdirp-native.js
|   |       |           mkdirp-native.js.map
|   |       |           opts-arg.d.ts
|   |       |           opts-arg.d.ts.map
|   |       |           opts-arg.js
|   |       |           opts-arg.js.map
|   |       |           path-arg.d.ts
|   |       |           path-arg.d.ts.map
|   |       |           path-arg.js
|   |       |           path-arg.js.map
|   |       |           use-native.d.ts
|   |       |           use-native.d.ts.map
|   |       |           use-native.js
|   |       |           use-native.js.map
|   |       |           
|   |       \---mjs
|   |               find-made.d.ts
|   |               find-made.d.ts.map
|   |               find-made.js
|   |               find-made.js.map
|   |               index.d.ts
|   |               index.d.ts.map
|   |               index.js
|   |               index.js.map
|   |               mkdirp-manual.d.ts
|   |               mkdirp-manual.d.ts.map
|   |               mkdirp-manual.js
|   |               mkdirp-manual.js.map
|   |               mkdirp-native.d.ts
|   |               mkdirp-native.d.ts.map
|   |               mkdirp-native.js
|   |               mkdirp-native.js.map
|   |               opts-arg.d.ts
|   |               opts-arg.d.ts.map
|   |               opts-arg.js
|   |               opts-arg.js.map
|   |               package.json
|   |               path-arg.d.ts
|   |               path-arg.d.ts.map
|   |               path-arg.js
|   |               path-arg.js.map
|   |               use-native.d.ts
|   |               use-native.d.ts.map
|   |               use-native.js
|   |               use-native.js.map
|   |               
|   +---ms
|   |       index.js
|   |       license.md
|   |       package.json
|   |       readme.md
|   |       
|   +---node-domexception
|   |   |   index.js
|   |   |   LICENSE
|   |   |   package.json
|   |   |   README.md
|   |   |   
|   |   \---.history
|   |           index_20210527203842.js
|   |           index_20210527203947.js
|   |           index_20210527204259.js
|   |           index_20210527204418.js
|   |           index_20210527204756.js
|   |           index_20210527204833.js
|   |           index_20210527211208.js
|   |           index_20210527211248.js
|   |           index_20210527212722.js
|   |           index_20210527212731.js
|   |           index_20210527212746.js
|   |           index_20210527212900.js
|   |           index_20210527213022.js
|   |           index_20210527213822.js
|   |           index_20210527213843.js
|   |           index_20210527213852.js
|   |           index_20210527213910.js
|   |           index_20210527214034.js
|   |           index_20210527214643.js
|   |           index_20210527214654.js
|   |           index_20210527214700.js
|   |           package_20210527203733.json
|   |           package_20210527203825.json
|   |           package_20210527204621.json
|   |           package_20210527204913.json
|   |           package_20210527204925.json
|   |           package_2021**********.json
|   |           package_20210527205156.json
|   |           README_20210527203617.md
|   |           README_20210527212714.md
|   |           README_20210527213345.md
|   |           README_20210527213411.md
|   |           README_20210527213803.md
|   |           README_20210527214323.md
|   |           README_20210527214408.md
|   |           test_2021**********.js
|   |           test_20210527205957.js
|   |           test_20210527210021.js
|   |           
|   +---node-fetch
|   |   |   LICENSE.md
|   |   |   package.json
|   |   |   README.md
|   |   |   
|   |   +---@types
|   |   |       index.d.ts
|   |   |       
|   |   \---src
|   |       |   body.js
|   |       |   headers.js
|   |       |   index.js
|   |       |   request.js
|   |       |   response.js
|   |       |   
|   |       +---errors
|   |       |       abort-error.js
|   |       |       base.js
|   |       |       fetch-error.js
|   |       |       
|   |       \---utils
|   |               get-search.js
|   |               is-redirect.js
|   |               is.js
|   |               multipart-parser.js
|   |               referrer.js
|   |               
|   +---npm-normalize-package-bin
|   |   |   LICENSE
|   |   |   package.json
|   |   |   README.md
|   |   |   
|   |   \---lib
|   |           index.js
|   |           
|   +---proc-log
|   |   |   LICENSE
|   |   |   package.json
|   |   |   README.md
|   |   |   
|   |   \---lib
|   |           index.js
|   |           
|   +---read-cmd-shim
|   |   |   LICENSE
|   |   |   package.json
|   |   |   README.md
|   |   |   
|   |   \---lib
|   |           index.js
|   |           
|   +---signal-exit
|   |   |   LICENSE.txt
|   |   |   package.json
|   |   |   README.md
|   |   |   
|   |   \---dist
|   |       +---cjs
|   |       |       browser.d.ts
|   |       |       browser.d.ts.map
|   |       |       browser.js
|   |       |       browser.js.map
|   |       |       index.d.ts
|   |       |       index.d.ts.map
|   |       |       index.js
|   |       |       index.js.map
|   |       |       package.json
|   |       |       signals.d.ts
|   |       |       signals.d.ts.map
|   |       |       signals.js
|   |       |       signals.js.map
|   |       |       
|   |       \---mjs
|   |               browser.d.ts
|   |               browser.d.ts.map
|   |               browser.js
|   |               browser.js.map
|   |               index.d.ts
|   |               index.d.ts.map
|   |               index.js
|   |               index.js.map
|   |               package.json
|   |               signals.d.ts
|   |               signals.d.ts.map
|   |               signals.js
|   |               signals.js.map
|   |               
|   +---supabase
|   |   |   LICENSE
|   |   |   package.json
|   |   |   README.md
|   |   |   
|   |   +---bin
|   |   |       supabase.exe
|   |   |       
|   |   \---scripts
|   |           postinstall.js
|   |           
|   +---tar
|   |   |   LICENSE
|   |   |   package.json
|   |   |   README.md
|   |   |   
|   |   \---dist
|   |       +---commonjs
|   |       |       create.d.ts
|   |       |       create.d.ts.map
|   |       |       create.js
|   |       |       create.js.map
|   |       |       cwd-error.d.ts
|   |       |       cwd-error.d.ts.map
|   |       |       cwd-error.js
|   |       |       cwd-error.js.map
|   |       |       extract.d.ts
|   |       |       extract.d.ts.map
|   |       |       extract.js
|   |       |       extract.js.map
|   |       |       get-write-flag.d.ts
|   |       |       get-write-flag.d.ts.map
|   |       |       get-write-flag.js
|   |       |       get-write-flag.js.map
|   |       |       header.d.ts
|   |       |       header.d.ts.map
|   |       |       header.js
|   |       |       header.js.map
|   |       |       index.d.ts
|   |       |       index.d.ts.map
|   |       |       index.js
|   |       |       index.js.map
|   |       |       large-numbers.d.ts
|   |       |       large-numbers.d.ts.map
|   |       |       large-numbers.js
|   |       |       large-numbers.js.map
|   |       |       list.d.ts
|   |       |       list.d.ts.map
|   |       |       list.js
|   |       |       list.js.map
|   |       |       make-command.d.ts
|   |       |       make-command.d.ts.map
|   |       |       make-command.js
|   |       |       make-command.js.map
|   |       |       mkdir.d.ts
|   |       |       mkdir.d.ts.map
|   |       |       mkdir.js
|   |       |       mkdir.js.map
|   |       |       mode-fix.d.ts
|   |       |       mode-fix.d.ts.map
|   |       |       mode-fix.js
|   |       |       mode-fix.js.map
|   |       |       normalize-unicode.d.ts
|   |       |       normalize-unicode.d.ts.map
|   |       |       normalize-unicode.js
|   |       |       normalize-unicode.js.map
|   |       |       normalize-windows-path.d.ts
|   |       |       normalize-windows-path.d.ts.map
|   |       |       normalize-windows-path.js
|   |       |       normalize-windows-path.js.map
|   |       |       options.d.ts
|   |       |       options.d.ts.map
|   |       |       options.js
|   |       |       options.js.map
|   |       |       pack.d.ts
|   |       |       pack.d.ts.map
|   |       |       pack.js
|   |       |       pack.js.map
|   |       |       package.json
|   |       |       parse.d.ts
|   |       |       parse.d.ts.map
|   |       |       parse.js
|   |       |       parse.js.map
|   |       |       path-reservations.d.ts
|   |       |       path-reservations.d.ts.map
|   |       |       path-reservations.js
|   |       |       path-reservations.js.map
|   |       |       pax.d.ts
|   |       |       pax.d.ts.map
|   |       |       pax.js
|   |       |       pax.js.map
|   |       |       read-entry.d.ts
|   |       |       read-entry.d.ts.map
|   |       |       read-entry.js
|   |       |       read-entry.js.map
|   |       |       replace.d.ts
|   |       |       replace.d.ts.map
|   |       |       replace.js
|   |       |       replace.js.map
|   |       |       strip-absolute-path.d.ts
|   |       |       strip-absolute-path.d.ts.map
|   |       |       strip-absolute-path.js
|   |       |       strip-absolute-path.js.map
|   |       |       strip-trailing-slashes.d.ts
|   |       |       strip-trailing-slashes.d.ts.map
|   |       |       strip-trailing-slashes.js
|   |       |       strip-trailing-slashes.js.map
|   |       |       symlink-error.d.ts
|   |       |       symlink-error.d.ts.map
|   |       |       symlink-error.js
|   |       |       symlink-error.js.map
|   |       |       types.d.ts
|   |       |       types.d.ts.map
|   |       |       types.js
|   |       |       types.js.map
|   |       |       unpack.d.ts
|   |       |       unpack.d.ts.map
|   |       |       unpack.js
|   |       |       unpack.js.map
|   |       |       update.d.ts
|   |       |       update.d.ts.map
|   |       |       update.js
|   |       |       update.js.map
|   |       |       warn-method.d.ts
|   |       |       warn-method.d.ts.map
|   |       |       warn-method.js
|   |       |       warn-method.js.map
|   |       |       winchars.d.ts
|   |       |       winchars.d.ts.map
|   |       |       winchars.js
|   |       |       winchars.js.map
|   |       |       write-entry.d.ts
|   |       |       write-entry.d.ts.map
|   |       |       write-entry.js
|   |       |       write-entry.js.map
|   |       |       
|   |       \---esm
|   |               create.d.ts
|   |               create.d.ts.map
|   |               create.js
|   |               create.js.map
|   |               cwd-error.d.ts
|   |               cwd-error.d.ts.map
|   |               cwd-error.js
|   |               cwd-error.js.map
|   |               extract.d.ts
|   |               extract.d.ts.map
|   |               extract.js
|   |               extract.js.map
|   |               get-write-flag.d.ts
|   |               get-write-flag.d.ts.map
|   |               get-write-flag.js
|   |               get-write-flag.js.map
|   |               header.d.ts
|   |               header.d.ts.map
|   |               header.js
|   |               header.js.map
|   |               index.d.ts
|   |               index.d.ts.map
|   |               index.js
|   |               index.js.map
|   |               large-numbers.d.ts
|   |               large-numbers.d.ts.map
|   |               large-numbers.js
|   |               large-numbers.js.map
|   |               list.d.ts
|   |               list.d.ts.map
|   |               list.js
|   |               list.js.map
|   |               make-command.d.ts
|   |               make-command.d.ts.map
|   |               make-command.js
|   |               make-command.js.map
|   |               mkdir.d.ts
|   |               mkdir.d.ts.map
|   |               mkdir.js
|   |               mkdir.js.map
|   |               mode-fix.d.ts
|   |               mode-fix.d.ts.map
|   |               mode-fix.js
|   |               mode-fix.js.map
|   |               normalize-unicode.d.ts
|   |               normalize-unicode.d.ts.map
|   |               normalize-unicode.js
|   |               normalize-unicode.js.map
|   |               normalize-windows-path.d.ts
|   |               normalize-windows-path.d.ts.map
|   |               normalize-windows-path.js
|   |               normalize-windows-path.js.map
|   |               options.d.ts
|   |               options.d.ts.map
|   |               options.js
|   |               options.js.map
|   |               pack.d.ts
|   |               pack.d.ts.map
|   |               pack.js
|   |               pack.js.map
|   |               package.json
|   |               parse.d.ts
|   |               parse.d.ts.map
|   |               parse.js
|   |               parse.js.map
|   |               path-reservations.d.ts
|   |               path-reservations.d.ts.map
|   |               path-reservations.js
|   |               path-reservations.js.map
|   |               pax.d.ts
|   |               pax.d.ts.map
|   |               pax.js
|   |               pax.js.map
|   |               read-entry.d.ts
|   |               read-entry.d.ts.map
|   |               read-entry.js
|   |               read-entry.js.map
|   |               replace.d.ts
|   |               replace.d.ts.map
|   |               replace.js
|   |               replace.js.map
|   |               strip-absolute-path.d.ts
|   |               strip-absolute-path.d.ts.map
|   |               strip-absolute-path.js
|   |               strip-absolute-path.js.map
|   |               strip-trailing-slashes.d.ts
|   |               strip-trailing-slashes.d.ts.map
|   |               strip-trailing-slashes.js
|   |               strip-trailing-slashes.js.map
|   |               symlink-error.d.ts
|   |               symlink-error.d.ts.map
|   |               symlink-error.js
|   |               symlink-error.js.map
|   |               types.d.ts
|   |               types.d.ts.map
|   |               types.js
|   |               types.js.map
|   |               unpack.d.ts
|   |               unpack.d.ts.map
|   |               unpack.js
|   |               unpack.js.map
|   |               update.d.ts
|   |               update.d.ts.map
|   |               update.js
|   |               update.js.map
|   |               warn-method.d.ts
|   |               warn-method.d.ts.map
|   |               warn-method.js
|   |               warn-method.js.map
|   |               winchars.d.ts
|   |               winchars.d.ts.map
|   |               winchars.js
|   |               winchars.js.map
|   |               write-entry.d.ts
|   |               write-entry.d.ts.map
|   |               write-entry.js
|   |               write-entry.js.map
|   |               
|   +---web-streams-polyfill
|   |   |   LICENSE
|   |   |   package.json
|   |   |   README.md
|   |   |   
|   |   +---dist
|   |   |   |   polyfill.es2018.js
|   |   |   |   polyfill.es2018.js.map
|   |   |   |   polyfill.es2018.min.js
|   |   |   |   polyfill.es2018.min.js.map
|   |   |   |   polyfill.es2018.mjs
|   |   |   |   polyfill.es2018.mjs.map
|   |   |   |   polyfill.es6.js
|   |   |   |   polyfill.es6.js.map
|   |   |   |   polyfill.es6.min.js
|   |   |   |   polyfill.es6.min.js.map
|   |   |   |   polyfill.es6.mjs
|   |   |   |   polyfill.es6.mjs.map
|   |   |   |   polyfill.js
|   |   |   |   polyfill.js.map
|   |   |   |   polyfill.min.js
|   |   |   |   polyfill.min.js.map
|   |   |   |   polyfill.mjs
|   |   |   |   polyfill.mjs.map
|   |   |   |   ponyfill.es2018.js
|   |   |   |   ponyfill.es2018.js.map
|   |   |   |   ponyfill.es2018.mjs
|   |   |   |   ponyfill.es2018.mjs.map
|   |   |   |   ponyfill.es6.js
|   |   |   |   ponyfill.es6.js.map
|   |   |   |   ponyfill.es6.mjs
|   |   |   |   ponyfill.es6.mjs.map
|   |   |   |   ponyfill.js
|   |   |   |   ponyfill.js.map
|   |   |   |   ponyfill.mjs
|   |   |   |   ponyfill.mjs.map
|   |   |   |   
|   |   |   \---types
|   |   |       |   polyfill.d.ts
|   |   |       |   ponyfill.d.ts
|   |   |       |   tsdoc-metadata.json
|   |   |       |   
|   |   |       \---ts3.6
|   |   |               polyfill.d.ts
|   |   |               ponyfill.d.ts
|   |   |               
|   |   +---es2018
|   |   |       package.json
|   |   |       
|   |   +---es6
|   |   |       package.json
|   |   |       
|   |   \---ponyfill
|   |       |   package.json
|   |       |   
|   |       +---es2018
|   |       |       package.json
|   |       |       
|   |       \---es6
|   |               package.json
|   |               
|   +---write-file-atomic
|   |   |   LICENSE.md
|   |   |   package.json
|   |   |   README.md
|   |   |   
|   |   \---lib
|   |           index.js
|   |           
|   \---yallist
|       |   LICENSE.md
|       |   package.json
|       |   README.md
|       |   
|       \---dist
|           +---commonjs
|           |       index.d.ts
|           |       index.d.ts.map
|           |       index.js
|           |       index.js.map
|           |       package.json
|           |       
|           \---esm
|                   index.d.ts
|                   index.d.ts.map
|                   index.js
|                   index.js.map
|                   package.json
|                   
+---supabase
|   |   .gitignore
|   |   config.toml
|   |   
|   +---.temp
|   |       cli-latest
|   |       gotrue-version
|   |       pooler-url
|   |       postgres-version
|   |       project-ref
|   |       rest-version
|   |       
|   \---migrations
|       |   001_add_structured_memory.sql
|       |   20250602203219_add_memory_fields.sql
|       |   
|       \---supabase
|           \---migrations
+---web-export
|   |   chat.html
|   |   conversations.json
|   |   file-1z2fAn5MFJc2MPv6z2vb58-image.png
|   |   file-257XZ3XhdaCEfyYPdEL9hw-bereal-2024-07-18-0454.jpeg
|   |   file-2793R4fDXap1KScQmoLreS-d8504b0e-7b3c-4560-bb60-f22ba244c592.png
|   |   file-2eJEaQ91NYv2vdpR9Tjfz8-humans-have-ideas-featured.png
|   |   file-2p6TEv7pEYjVPMtKYg9HiW-1000013069.jpg
|   |   file-386W4RzdgwQCZGAt2mv7Xw-5f83895b-fe83-48b8-9c86-39a12da91ed9.png
|   |   file-3DdzdaKgUb3cPRyi6upxHd-77ea1537-3e46-4d61-af69-cb460d2c07f0.png
|   |   file-4jjingMFrxuGJxBBCRzXNh-33d47c22-201f-4080-8440-d666ec964423.png
|   |   file-4TpwMSvHc494pdoE8xMHXH-2025-05-01_18-03-14.jpg
|   |   file-5AEaQV61syubQUishD1wE7-image.png
|   |   file-5MpyAjsJQPHCHTT2Kqn88r-humans-have-ideas-featured.png
|   |   file-5tVZWw2gyfUe9UBFVoCBdB-image.png
|   |   file-5VbMW5DEKAzgRL9PMVNuLo-ethics-and-ai-featured.jpg
|   |   file-6C6BaHjfZuBzsqky4fiVJd-humans-have-ideas-featured.png
|   |   file-6K9ft2dv5UHJ2LtXoncVbh-09d3b27d-6df1-480f-a17b-f05142976272.png
|   |   file-6rFQiAq1Z17bb18RqoWzwU-PXL_20230925_191721155.jpg
|   |   file-6TVRCzxPBncRtpXWn48sgr-d0f8a317-aaf2-434e-90cb-65fc63ca8419.png
|   |   file-77zfMKPCJTn1aoukhqoiZM-975bef65-0d33-4282-855d-0c926922d7f4.png
|   |   file-7hyUBwg27AfFKhqN22WVs9-wider-image.webp
|   |   file-7M2oupqp7vLkC3wicKvRas-85bb67b4-10e7-4cb9-a0a9-3447c8f4f5db.png
|   |   file-7qiLfmZyECYFtFJdSM8cYi-88e96d3a-7809-47ef-af8b-af7a5aab76a4.png
|   |   file-7vRrn7Z85GzjXXWr6sYGjX-8da1f027-53a7-4ed8-b0b6-948751a70498.png
|   |   file-8YXGSHfk43fBQS7D2XraM2-humans-have-ideas-featured.png
|   |   file-95e6zNP9SV8Gi9PFMRmSSa-0a548645-fdc2-4209-a0de-9a99c2b0dfd1.png
|   |   file-9KtVYnpd7h5PzVZDsQA6Pp-perktracker1.jpg
|   |   file-AbgeMoigJ7H7FbT5xQ4Xbb-humans-have-ideas-featured.png
|   |   file-AukDaSqCuQutGj1BAHuy7M-4d83c875-f7fd-49a7-af84-56166b73f10a.png
|   |   file-AYZcJFBdRYnWguYGTxZZxf-76f6cabe-5550-4a1e-ba54-3ad047c85cab.png
|   |   file-BStMRngEcQmTmRVwgZNVVA-image.png
|   |   file-Bu5ZX6Cn1VNhF8BGTvFWZB-PXL_20250203_050832472.MP.jpg
|   |   file-BuPxzNxyTodHgeQ9HWvNxf-64294098-51b3-4e0a-a456-f00bc86aa01a.png
|   |   file-CpyyzKGhdvqVGab4zGNv1H-1000006276.jpg
|   |   file-Ctn5VJmsMNahWh6QzYhu4r-d0757a0c-7f98-42ef-878e-9343795035f1.png
|   |   file-CUxSMZF2fGD9NPa5siXVRA-image.png
|   |   file-CYbrmeG2eH2BP772Jmxssb-aaaf21ed-03f8-40b6-93f3-ca400076f7a4.png
|   |   file-D7EFFTAUeqdaqhNtATndL1-4a5b0d5d-2013-457a-b098-7b55fc67fb84.png
|   |   file-DherErzY2TL3LPEgzbJKDV-5462ba66-90a7-4e94-8a48-50a27bfc5866.png
|   |   file-DhVwd5jB1pPgcZkyagJYwZ-image.png
|   |   file-DTxQsaa5eeG7xTNABQ4qRt-0e61ba87-3f42-417f-ba37-50b962b0c11a.png
|   |   file-Dz65dpj3VoCJodnMgk4bAP-unnamed.png
|   |   file-EGH3cHYf4SXhM4XAhVZ14B-c67b2f58-a072-4aed-87d2-ebed141c7fd0.png
|   |   file-F5pvVbhzTYzsXiqHdNxXwu-humans-have-ideas-featured.png
|   |   file-F5vrhPUWDQvxMKYEiEJQjB-8c58163c-dd5f-4dc7-95e4-ea2d170da19c.png
|   |   file-FC3p2cs4Tr2V9jki8jCzpZ-humans-have-ideas-featured.png
|   |   file-FKnnM8jHsYpwmgsCvKXNwo-2025-03-19_15-48-09.jpg
|   |   file-FNTfzfBZQYjFACJVEjeLHp-848a9a82-cee3-4bef-9358-1e00c2e2c9c7.png
|   |   file-FW4e1TysnbAY8rUJSmtS7e-image.png
|   |   file-FytTaswzZoDL2G5v41qcGf-humans-have-ideas-featured.png
|   |   file-GreWYjsBVRMKmvpQMr8Ppx-9066a011-ef00-4f2d-a2ca-3629f01ff25c.png
|   |   file-GVNwJtUXe1DfeSFnH352Sm-image.png
|   |   file-HkovGSJnFoiXhngYmPSXgM-ai-guardrails-featured.jpg
|   |   file-HKt4oJMiaovR69hzvr8ERg-f2c07af0-f768-4319-9021-b3d4bcb26090.png
|   |   file-HVC6MBXKeaZsbwB4ss6933-c7608ce9-f010-45c5-9447-6afc1ed79203.png
|   |   file-JccJHYQbPPrM6TKaLcWq4r-image.png
|   |   file-JFFYKYLTyqS5bw1PyZqHVG-2025-05-01_18-02-19.jpg
|   |   file-JKrRAWtt13zpXHPkVjxzJM-aa19704b-0b40-4052-9bcd-a1878e07ea81.png
|   |   file-JwvmUDiuHAskUAdGj1SYbd-d9ca7f90-3c49-4f80-959b-0909a4a43e36.png
|   |   file-KdArxqj64tczgwaqX1vKH9-6dc92549-4560-4354-8a84-9a13c0a901ed.png
|   |   file-KKkcnUdiZ6UotDFCmhPzgc-1000013074.jpg
|   |   file-KPBYnL8iLwZW7PHgD5Z9Ry-perktracker1.jpg
|   |   file-KQJB3jCi4SAvezHe89qELd-humans-have-ideas-featured.png
|   |   file-KWEBEvPo6TLLVN3cnWkKPY-image.png
|   |   file-KwYFAwFGFdL1RLwxjnLQpp-25255943-8297-4d4a-8e32-cea849ddaadd.png
|   |   file-KYnLqbUYjzMTwMWx9yCHEC-d232af2e-6b86-4740-85d1-8f394ce7379b.png
|   |   file-KyT8AaHMpmVoVWqf3ayV2p-PXL_20230925_191721155.jpg
|   |   file-L3ksAeDMYF7PtiansN3u7e-image.png
|   |   file-LWWDaDWH16T4nPPt88uUco-image.png
|   |   file-Lyi2zc5Lhk7f3Lma45FkjS-PXL_20230925_191721155.jpg
|   |   file-MEbVWDebK16EiuirpbXkhZ-933f9da5-590c-42e0-86cc-da98f502798b.png
|   |   file-Meh3d3tFurdggPMkCJThmY-profile.jpg
|   |   file-MjyqH47YJXP6kuXbicKN3f-1000013435.jpg
|   |   file-MqcGLb5Z3H9Wni25oU6BKF-d04bcb9b-a526-48fe-86fb-200a3fca03dc.png
|   |   file-MRvGpdoX88S4cCfYGB5uvV-32bbbd9a-1856-4bed-a4d9-45c614813299.png
|   |   file-N8tP4HveCGM9Nq5tFDe6zW-9891b557-c252-4517-9e98-6e87920529d2.png
|   |   file-NDYg9t7FRsgj6tgDB12N4a-83225db5-edcd-49a7-9b94-213f1c52a526.png
|   |   file-NewZTYPRfnoUqZFfY3VYuN-04dae905-7b35-4c0c-b268-7de55705678b.png
|   |   file-NsEWoZUeC9x1L9uRvvritx-89eafd24-f044-4486-b99a-ac9aab2d73c9.png
|   |   file-NXu2nE72VrgVjTo3yB5YGT-7aefc3dc-13bc-4752-b2f9-d0477ab4cc8c.png
|   |   file-P98Lxrj6DuQNgncVbDKX1R-1f2fe25d-090f-44f5-971c-47d980b5dd5c.png
|   |   file-Q5yDpovQZgpUEBnjt5u1qw-1000006542.png
|   |   file-QDe1zicM2HDDyRq1Jzur7R-image.png
|   |   file-QJ7MMEByYgxrkduqQHgK1v-4f75ed12-5347-488d-9443-aa234799731a.png
|   |   file-QoWVYbWTPBBcvLbwfDcKTN-d156d3f4-6f08-4976-9ec0-39abe644d997.png
|   |   file-QV3kX5niMkMYs2eCiwjjxV-16101ec1-f587-4d82-ad47-42a68510dd18.png
|   |   file-RA3pwggqC8wtT9sxDZkLHX-ethics-and-ai-featured.png
|   |   file-RstptGhqmWC1hT5ejJvNLT-1000013067.jpg
|   |   file-RUg9vGbeLyKuYGZ7Nz69wv-Blank diagram.png
|   |   file-SGMdMzr5s37dspzfq3wnJJ-32fe6abd-fd3a-4c64-a5b7-39538a0af050.png
|   |   file-TFsHwF8ZpDjkarsqBWoHGd-6fef0153-6dd9-42a9-8fa6-9df6acf66e99.png
|   |   file-TN7TzAg4JjG4t9mRgAgktQ-image.png
|   |   file-Tsn9CVPpKNFR7T9Xq6Gmkr-094076ce-d554-410d-8642-698baebe923a.png
|   |   file-Uanqzf2KaJujZD6sqz1HtY-PXL_20230925_191721155.jpg
|   |   file-UiXtQt3TWHLc1fUpFei2jr-image.png
|   |   file-V9g2kyczTdf4uxxo6ocq5t-1195dbeb-58f5-4640-9f0f-bcd67607b7de.png
|   |   file-VEKnJSXcUfe8WSUxg4ARGr-image.png
|   |   file-VJWDL7Vig9v5wmbQSkGZvK-image.png
|   |   file-VWgb5D2Ab7moXW9QiSMAGM-3112ed05-af18-4421-bd53-fd3148c5a4ba.png
|   |   file-W1Mq5hiARmbUPQGscD2suY-1000006132.png
|   |   file-WCkpxhvu364rM15uhLz2eq-image.png
|   |   file-WWwJrUTNZFx55UqZmYVBPy-image.png
|   |   file-WYcTUtnq5ShCapAsgsncw6-operation-save-sparky-featured.webp
|   |   file-X5xNRZ6Mf9fV2B5y2yLsEm-10ef3e4c-5119-42a0-9c9a-35c354bb1af7.png
|   |   file-Xb6i1cAXSWgBu59hrAQW8T-image.png
|   |   file-Xjkcu9JkHyqAWKkSpbwoJD-9ea7b22e-940f-4543-95d5-22e2e35a4c99.png
|   |   file-XWqX69L9ZrhcPC7y9uLhGB-0bc3b90b-2fcd-40cb-ba4d-831ac8ccab37.png
|   |   file-YS5cjRu9VH64UTzVkz93xb-image.png
|   |   message_feedback.json
|   |   shared_conversations.json
|   |   user.json
|   |   
|   +---dalle-generations
|   |       file-1wRA1tdGAQFS9dzatfrtba-d9fd4263-8482-4da3-8428-c1c892025109.webp
|   |       file-38TXSWsa7MJV1WrCDtB6Zu-b9abd98e-1c57-4fc3-87e0-4f694dc4f80b.webp
|   |       file-3e8gCwPE6maG1C3dCQqmya-b9ad6ab1-5dce-4677-8c87-003283ff15f9.webp
|   |       file-62i9rfyTJ8bmjntc8RWvZr-d3ad6855-f9a9-4c30-9458-0140a5ca3a97.webp
|   |       file-7Kstmzz74irSfarLUxqyJg-7da3ee8d-5593-4ff0-b0a6-54f666a9c60b.webp
|   |       file-8VDsfvWmyfkjivrZ5pijzf-78f73799-b2ca-453b-994f-1e276a7b4b79.webp
|   |       file-AjJNpEEGBZ2mzzn7SR4f1E-f2a1eef0-bffc-4cfb-990c-72ba0a233a04.webp
|   |       file-BEe2cMKYSFAg4uB9rugq6a-a7d7af75-85e9-401c-af25-028f4ea625bc.webp
|   |       file-C5FCiUaxtwLteYupGXwjud-60ff7fde-d845-4d73-9153-2a32fa9d9b2b.webp
|   |       file-EffBwXgFz8ytQgyE7k1WsT-30039454-b66e-4e22-935c-642840858a46.webp
|   |       file-Ei8wGYxtjDGQd4QkmYegq3-365c3578-33b7-4e1a-aa3b-d5e649dd8f45.webp
|   |       file-Epj34LttUcyQ7DbuiUMi5S-7ac03cd0-7a79-4332-b047-95d957ae2052.webp
|   |       file-EsuvgVfPkCFHZWFLVvXmgs-5695ecf1-7c1e-49d7-b99f-2d3d3f473d38.webp
|   |       file-ESy73gbZvntnawZ5FAsPPR-9b125c3f-7902-4ec0-a53c-4dec12767a7e.webp
|   |       file-EZ66eCYhwjRur9G5R7eSuz-5e8826de-818b-487a-a101-143964b45027.webp
|   |       file-FTf7XvKj9UsSbdfNabY5SX-cc482919-655e-4bd3-88b2-5a5ff80a9254.webp
|   |       file-GdPqx2bUgU4745nbSzYcWt-5ab52be3-d3fd-455f-964b-fc998b8eb657.webp
|   |       file-GwRh9ekeWpUN5BhTnSd4TG-1127a100-e22f-4a19-89f7-a932421100f9.webp
|   |       file-Jmb6QQ8o4iumykmzxM8TMF-65738b3f-b404-467b-9b1c-d678d0233378.webp
|   |       file-K66C58T4PxaH5hgtfKD9Vt-f19a17dd-1f07-4644-bd61-684464f06a07.webp
|   |       file-KzMYMkgBcAoKXFQJhetwgt-22cdff92-5de5-421d-8cb2-554a8588cd7f.webp
|   |       file-N9BBgVzY59J877DNeWBJB7-b4bfa55c-ecb2-42c3-a91c-242ea928492b.webp
|   |       file-NbpnTfhhBVPX6hC8xjA98w-e770ebe5-043d-47d6-ab24-cc52238c6745.webp
|   |       file-Q7Ur8bEewFawYdSz8nQbSv-30e3dfba-e8ef-4563-99c7-6b47e4fcc0ca.webp
|   |       file-Rh2rZYvFVo5Gtb7rZV1nnu-f47b67a9-1b5f-4af8-8beb-79c42e955909.webp
|   |       file-SnknUjGLV7Mf3YPoU6UmsQ-14b310d2-0b34-4c8a-b9ce-97520a92d882.webp
|   |       file-T75qw4cvUGfLsLTRusUm9S-bcd37944-7d2b-460a-99e8-d2de7d706532.webp
|   |       file-TwCbYs35FfvdTQkTRPurBe-2e86fa2f-b8f1-43af-9094-7113be1c359f.webp
|   |       file-UHnBBHSbsSeWYTirAZjz78-1c6352fb-d124-446e-ad0c-04fa77e6e581.webp
|   |       file-VcGf1EwafS8ESxintbczGd-8cf66bbb-c225-499b-9b5d-a0f229d5ad52.webp
|   |       file-VCq3o9mTV6yBWSHoBe4dFR-f1fb15af-68e7-490b-a933-7fa6964e5492.webp
|   |       file-VoAmD8yZYzdaysdD8cniNd-4e90e152-df90-42b6-b58f-863a7e609b1e.webp
|   |       file-VZWrBcS3Wo7incadZhXj3Y-7008c67c-2ee2-43fd-873c-ebc295b4c734.webp
|   |       file-XeU7GKw4NHw91nzbYA398n-5f4dcf74-7435-4db7-915b-bb21048fb0d2.webp
|   |       
|   \---user-i1vpSFC0EdW2blCT825W1Jdv
|           file_00000000032c61f88f05fd824ade08df-7d6879dd-66da-4c94-8121-c40e39ed540d.png
|           file_0000000005e051f692a8af64e27db203-89330b2f-4b7e-4862-b607-a2416af34210.png
|           file_000000000828623082a581ed4fcb72c7-2a998af2-7a9a-4047-b005-82eeb5c879ba.png
|           file_0000000008346230bc01485c74e16072-c40dbf6e-0048-4b72-b844-61e1c20ba7b8.png
|           file_000000000a1062469d0ba5eac6db4e56-708c5378-719f-4dc0-a2be-01d262d14711.png
|           file_000000000b3061f7974dbd67709284db-73f901ca-d71b-4a67-a0c1-8da10a4a33e2.png
|           file_000000000d705230b5368ed487688675-ec34ed78-f549-4a97-a4ff-163fc6d805aa.png
|           file_000000000df8622f91497efff397caf8-1b9a2401-e1ad-40e3-8ffe-e9c7ee4365c3.png
|           file_0000000014dc6230879a41d826c5c001-fe56afa4-cb77-43a3-abcf-d1f568fba439.png
|           file_00000000151c51f78d336e37495a05bc-03971bb6-27e1-40c9-9709-2a6bc594ed57.png
|           file_000000001a4c62308ba868eefe8681a7-ff330214-ea28-43d2-9064-92b2b7cb9967.png
|           file_000000001a54622fb96f6da228b8b0df-f8f14b28-eed2-496c-83cf-14863de029cf.png
|           file_0000000020f461f887272b1e9d5a8853-5b91352c-5aad-458e-befe-ad1a6f904a60.png
|           file_00000000270061f88bf09f00a76d661d-117fee8e-734b-4f47-9163-383eab58402e.png
|           file_000000002a5461f786e8745d0481b5d0-d068512e-6c07-493b-b117-ea619ffc757a.png
|           file_000000002bf061f7bbd25521746dd3ac-5c69a64e-4a09-48f3-b2ae-eb51107fe5c4.png
|           file_00000000303c62308fcdb45015145bbb-35d1d263-3ac2-4e6c-b341-8ef61c2def40.png
|           file_0000000031b8622f8d82f70242b3f34d-d06230b6-1d67-4108-90c9-028b2e51556a.png
|           file_00000000328c6230abaf71c5fce75998-e758acbb-e04d-433c-8ceb-2ed33cddcf50.png
|           file_00000000361061f8ae2a8c23f9396516-c830f593-ef7b-4d1a-9392-efbc65020068.png
|           file_000000003d9c61f7b47371dd3edcbb9a-609a892f-d413-4c07-a7c1-d1f26f2d23d6.png
|           file_000000003dac620abf730299ddfc2f6b-aa78e8d0-7fb1-48b4-9c0e-ca91b4ba932e.png
|           file_00000000401861f8834e18f2f6b915b2-b3e8026c-9e3e-4e4a-be17-305a7c8cfd0f.png
|           file_00000000415461f588f9ca2deccb44df-029dd232-4a65-4fc3-af48-086ce97dffe9.png
|           file_0000000043bc6230b7c0827c70cbc821-cae6f98d-de01-48db-b35b-4c3c4990c6dd.png
|           file_000000004560622f93bb2135f970485e-03a7d83a-51fe-41bc-93d7-741a112b1d94.png
|           file_00000000459862308f771ac64d819d76-9e14e597-69f0-4e3a-ae18-f86d07257e49.png
|           file_0000000045e861fd8c30408b466cf1ea-a7d9f21d-10b5-41ad-9884-6f41e37ed187.png
|           file_0000000048986230a87e4dda1b105ac5-3fe6b05b-4be7-4dc2-911a-137b1d13b479.png
|           file_000000004a8851f6b621209563782b61-d989a569-733b-4c8b-8eb9-f8fc8be3a6e3.png
|           file_000000004bec61f599410e963c48f0a5-830b580d-89be-4afe-912d-b99d3ed65dba.png
|           file_00000000509851f6b95891266280f772-fdec12e2-3d99-4d0a-a85c-f5bbc3570d8d.png
|           file_00000000512061f780253d313a1de9dc-13417faf-dfc7-44d8-8ea3-725639e9437f.png
|           file_0000000056a061f58bbd0827cbeb1b30-98f65594-ee04-4a9d-921b-687930ec37fb.png
|           file_00000000581062309316edcb79e87312-b85a619c-ba76-404e-9e3d-4c1fba728180.png
|           file_000000005b70623082d0083bb6f0604e-c49cba06-2d0c-4493-81ce-c02c55a4a2e6.png
|           file_000000005b7462308b7c29d570a34d78-d4ecef58-fad9-4f13-9b13-4fdb564dbdb1.png
|           file_000000005c2051f6bcc2a9ffd5e4121a-947505cf-16c4-45eb-bb4b-826d2e9616e8.png
|           file_00000000640851f68a026a6c45e381be-169d2bf1-e9d9-44bf-8d4d-ff28dcf2e709.png
|           file_00000000645861f784ffcceb14dcbd20-d8f0270f-82c3-4e7a-9c10-e32343fa6c95.png
|           file_0000000066d461fdb8e803ecf3bf69a8-4689cdf4-e283-4bef-9b03-b346fdb10630.png
|           file_000000006cfc61f7b03dcfe2eb8c882c-2f305a41-61bd-4d97-be67-3d4515515019.png
|           file_00000000744c6230baee8bd19ef2f9aa-44a546b0-3d52-4068-a750-20f3ad78fa8e.png
|           file_00000000749851f68b7f594c8702692d-71c61db4-2d34-42e5-8522-ebfc5d7770e4.png
|           file_00000000783c622f8dd59e05a22b7ffc-6cc92f00-9bba-4d69-92dd-26a093bef5c8.png
|           file_000000007d0c51f79031955313e98e5c-b5478a06-9a96-4d04-a4ac-6bc4c06b4867.png
|           file_000000007d2452308598c9c205be0e10-dfe33b46-704b-4ffb-ad23-5abeef75cc9c.png
|           file_00000000806061fba3ad67536c26d602-2327b274-616d-4282-917e-21db28c9e837.png
|           file_0000000087486230a7b6625dd9c59977-e796bcbb-9ab2-4b01-ac3c-a864809a2231.png
|           file_000000008b5461f7ba4d6a38547e6ee2-0da39cf1-9b9a-4000-94c6-0dc54874d599.png
|           file_0000000091bc61f887bfd60f017a2089-f5661f2f-6079-4b7f-9425-6420bc14a1b3.png
|           file_00000000932851f780861003691fc251-bd14385e-f20f-402a-a0f5-c4d96d94d8de.png
|           file_00000000957461f7a70db62da3e8a569-fe817756-49b8-4bf4-984e-ed1f556addd8.png
|           file_00000000978c61f7a26aac02a761dd8b-a576fe88-dc5d-4b54-b74c-d7d22308d6c1.png
|           file_00000000979861f5adfd0203a01a09ee-9b3d3edd-e0cc-4b5d-b791-b4b39f19327e.png
|           file_000000009994624691207a71f4f3bf4f-b742d127-14f3-4fbd-842d-65fc12487d9a.png
|           file_000000009d106230baf54403ef5542f0-3600cd27-3a98-4105-b43b-d98f4f675864.png
|           file_00000000a14061f896c0d1aaebf4eb24-1f09d178-ef68-4f3f-8ff9-e328f0938f5a.png
|           file_00000000a14c61f7972ebe45fd4c1501-6491ad71-e5aa-493c-bd37-1ad6e528d7c1.png
|           file_00000000a1f461fdbc67480bda54fdba-eb87030d-6c25-4bbf-83b0-8d926d5da423.png
|           file_00000000a52861f7a51ae002e67dc2d5-f34f5aa3-a88b-4226-917d-063d3386b578.png
|           file_00000000aa8c61f5836963f6a5bdb38e-a25ec2af-7201-4162-8c15-859a3c33a5a1.png
|           file_00000000aaac6230914ae5941f906190-356e9d73-8aac-4adb-add1-e1957846de14.png
|           file_00000000abf051f6bc704baa7538435f-a3028911-6c26-47c8-8ddd-ab966e19891f.png
|           file_00000000b22c61f5b97d90c159fb9bbf-971a5857-d0d0-4551-8403-cd9e66f78511.png
|           file_00000000b6e861f7b0a71b30025e5c95-ff7df36e-dac3-4c7e-8e41-8f3eb3a46dd0.png
|           file_00000000b83451f7809a2b342f055086-2ed16bc4-facb-4d6e-b348-ba5dbc8f6504.png
|           file_00000000b908622f8d102dea2c78c25f-823b57c4-dd81-42ff-b507-c8ae97ab8b19.png
|           file_00000000bbb461f9af529c64158da8a5-bba40266-bede-4c7c-b983-dfded4b645d5.png
|           file_00000000bbc852308c78efacebdab62a-f43f5008-dae1-449d-ad46-1bd8fd4513d6.png
|           file_00000000bbd451f6ab8c1da2824a8e3a-8e9e182a-982b-45f3-84cb-cee5d7031eed.png
|           file_00000000be7051f6baf2d409d6a99b3a-a24a213d-be29-4c17-b993-b9aab9eb883f.png
|           file_00000000beac61f7819741e3abc9d26c-56ba08ea-d690-491e-9908-2eb1579bde02.png
|           file_00000000bf6861fda836893c627f49f4-450df5f5-1362-4235-8cc5-f4b74ffcfcd4.png
|           file_00000000c17c62308268d03afdf0bc5f-d5f820b0-eaa0-4313-b97c-5738c0354338.png
|           file_00000000c2d46230893c6e8d98f8e7e3-1b89962f-17d3-4652-bb6d-4e5d47ac463c.png
|           file_00000000c31451f696fca6316fc58d1f-5d05edd8-e589-4f07-8a3c-ac94ad1f12e9.png
|           file_00000000c84051f7aa7896ce4145434e-2d7f5b46-f3cb-4f51-87c8-dc2c06f54882.png
|           file_00000000ce5c6230854edb203c9942c5-6efc0051-e698-4e59-aa5f-e61871592ff6.png
|           file_00000000d2e061f7bbc9b573ea9191c4-5ce63aac-b19d-4ff9-9d4a-651e5c4eff76.png
|           file_00000000d41851f7a4645e122ece5fcc-21f1d07b-83bd-4a89-8e4f-08239766683e.png
|           file_00000000d70861f7a02576135a4baf2a-122e790f-95f7-442c-9d47-0688cb54f821.png
|           file_00000000ea6462308a16667d4a68c00e-985c44bc-9c5a-4d82-8910-b4af41f6e991.png
|           file_00000000f4145230be5338724e342a30-facc97c0-5b7a-478c-a356-140bf5434f74.png
|           file_00000000fb506230bafbf69c6770f466-8a287415-3458-449f-9bb3-7e288bb5dc84.png
|           file_00000000fd7c61fa9d29a00b8111c210-c4939f6f-f61a-4527-8bda-cf5dc32f6e6e.png
|           
\---__pycache__
        config.cpython-313.pyc
        helpers.cpython-313.pyc
        inject_memory.cpython-313.pyc
        load_memory_batch.cpython-313.pyc
        retrieve_similar.cpython-313.pyc
        
