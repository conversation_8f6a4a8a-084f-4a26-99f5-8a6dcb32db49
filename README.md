# AI Assistant Memory System

A Python project for managing AI assistant memory using Supabase and OpenAI embeddings with vector similarity search.

## Features

- **Structured Memory**: Store memories with metadata (type, tags, source, importance)
- **Advanced Filtering**: Retrieve memories by type, tags, and importance level
- **Semantic Search**: Vector similarity search using OpenAI embeddings
- **Row Level Security**: Multi-user support with proper data isolation
- **Async Operations**: Optimized for performance with async/await
- **CLI Interface**: Full command-line interface with argument support
- **Environment Configuration**: Secure API key management
- **Error Handling**: Robust error handling and validation

## Setup

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Environment Configuration

Copy `.env.example` to `.env` and fill in your API keys:

```bash
cp .env.example .env
```

Edit `.env` with your credentials:
- `OPENAI_API_KEY`: Your OpenAI API key
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_KEY`: Your Supabase anon key

### 3. Database Setup

Create the `structured_memory` table in your Supabase database:

```sql
-- Enable the pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Create the structured_memory table
CREATE TABLE structured_memory (
    id BIGSERIAL PRIMARY KEY,
    content TEXT NOT NULL,
    embedding VECTOR(1536) NOT NULL,
    type TEXT NOT NULL DEFAULT 'fact',
    tags TEXT[],
    source TEXT DEFAULT 'manual',
    importance INT DEFAULT 1,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE DEFAULT auth.uid(),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE structured_memory ENABLE ROW LEVEL SECURITY;

-- Create security policies
CREATE POLICY "Users can insert their own memories" ON structured_memory
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view their own memories" ON structured_memory
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own memories" ON structured_memory
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own memories" ON structured_memory
    FOR DELETE USING (auth.uid() = user_id);

-- Create an index for vector similarity search
CREATE INDEX ON memory USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- Optional: Create a function for similarity search
CREATE OR REPLACE FUNCTION match_memories(
    query_embedding VECTOR(1536),
    match_threshold FLOAT DEFAULT 0.1,
    match_count INT DEFAULT 3
)
RETURNS TABLE(
    id BIGINT,
    content TEXT,
    created_at TIMESTAMPTZ,
    similarity FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        memory.id,
        memory.content,
        memory.created_at,
        1 - (memory.embedding <=> query_embedding) AS similarity
    FROM memory
    WHERE 1 - (memory.embedding <=> query_embedding) > match_threshold
    ORDER BY memory.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;
```

## Usage

### Inject Memory

Store memories with metadata:

```bash
# Basic usage
python inject_memory.py "The user prefers dark mode interfaces"

# With full metadata
python inject_memory.py "Emily prefers concise answers" \
  --type preference \
  --tags "emily,communication" \
  --source chat \
  --importance 4

# Store project information
python inject_memory.py "PerkTracker uses React and Node.js" \
  --type fact \
  --tags "perkTracker,tech-stack" \
  --source documentation \
  --importance 2
```

### Retrieve Similar Memories

Search with advanced filtering:

```bash
# Basic search
python retrieve_similar.py "interface preferences"

# Filter by type
python retrieve_similar.py "team information" --type identity

# Filter by tags
python retrieve_similar.py "technology stack" --tags "tech-stack,perkTracker"

# Filter by importance
python retrieve_similar.py "critical information" --importance-min 4

# Combine filters
python retrieve_similar.py "project details" \
  --type fact \
  --tags "perkTracker" \
  --importance-min 2 \
  --limit 5
```

## Project Structure

```
.
├── requirements.txt          # Python dependencies
├── .env.example             # Environment variables template
├── config.py                # Configuration management
├── inject_memory.py         # Memory injection script
├── retrieve_similar.py      # Memory retrieval script
└── README.md               # This file
```

## Technical Details

- **Embedding Model**: OpenAI's `text-embedding-3-small` (1536 dimensions)
- **Vector Database**: Supabase with pgvector extension
- **Similarity Metric**: Cosine similarity
- **Performance**: Async operations for optimal throughput

## Error Handling

The system includes comprehensive error handling for:
- Missing environment variables
- API connection failures
- Invalid input validation
- Database operation errors

## Security

- API keys stored in environment variables
- No hardcoded credentials
- Input validation and sanitization
